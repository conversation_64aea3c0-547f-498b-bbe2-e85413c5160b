package imca.servlet;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import imca.database.DatabaseServiceController;
import imca.util.Constants;
import imca.util.ConverterHTML;

@WebServlet("/viewallparts")
public class ViewAllParts extends HttpServlet {
	private static final long serialVersionUID = 1L;
       
	
	protected void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		doGet(request, response);
	}
	
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		HttpSession session = request.getSession(false);
		
		if(session != null && session.getAttribute("isAuthenticated").equals("true")) {
			DatabaseServiceController dbService = new DatabaseServiceController();
			String htmlContent = ConverterHTML.convertToHtmlTable(dbService.viewTable(Constants.QUERY_DISPLAY_STOCK));
			
			response.setContentType("text/html");
			response.getWriter().append(ConverterHTML.formAction("back")).append(htmlContent);
		}else {
			response.sendRedirect(request.getContextPath());
		}
	}

}
