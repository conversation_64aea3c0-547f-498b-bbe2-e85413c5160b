package imca.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class DatabaseConnection {

		private static Connection connection;
		
		private DatabaseConnection() {}
	
		public static Connection getConnect() throws SQLException {
			
			if(connection == null || connection.isClosed()) {
				try {
					String url = "**********************************************************************************************************";
	                String user = "sa";
	                String password = "P@ssword@321";
	                Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
					connection = DriverManager.getConnection(url, user, password);
				}catch (Exception e) {
					System.err.println(e.getMessage());
				}
			}
			
			return connection;
		}

}
