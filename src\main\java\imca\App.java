package imca;

import java.io.IOException;

import imca.controller.InventoryManagementServiceController;
import imca.database.DatabaseServiceController;
import imca.util.Constants;
import imca.util.InputHandler;
import imca.view.InventoryManagementView;

public class App {
	
	static {
		
		InventoryManagementServiceController service = new InventoryManagementServiceController();
		
		try {
			service.init();
			DatabaseServiceController dbService = new DatabaseServiceController();
			
			int count = 1;
			
			while(count <= 3) {
				System.out.print("Enter Username: ");
				int username = InputHandler.readInt();
				
				System.out.print("Enter Password: ");
				String password = InputHandler.readWord();
				
				if(dbService.auth(username, password)) {
					System.err.println("Successfully Logged In..." + "\nWelcome! " + Constants.properties.getProperty("userName"));
					break;
				}else {
					count++;
					System.err.println("Unauthorised Access...");
				}

			}
		} catch (IOException e) {
			System.err.println(e.getMessage());
		} catch (Exception e) {
			System.err.println(e.getMessage());
		}
	}
	
	public static void main(String[] args) {
		
		InventoryManagementView screen = new InventoryManagementView();
		
		if(Constants.properties.getProperty("userName") != null) {
			screen.homePage();
		}

	}
}
