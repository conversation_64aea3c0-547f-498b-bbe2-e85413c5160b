package imca.servlet;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import imca.database.DatabaseServiceController;
import imca.util.Constants;

@WebServlet("/deletepart")
public class DeletePart extends HttpServlet {
	private static final long serialVersionUID = 1L;
       
 
	protected void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		doDelete(request, response);
	}

	protected void doDelete(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		String partNo = request.getParameter("partNo");
	    int updatedBy = (int) request.getSession().getAttribute("userId");
	    //response.getWriter().write("Missing partNo or userId." + partNo + updatedBy);
	    
	    if (partNo == null || partNo.isEmpty()) {
	        //response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
	        response.getWriter().write("Missing partNo or userId." + partNo + updatedBy);
	        return;
	    }
	    
	    try {
	        DatabaseServiceController dbService = new DatabaseServiceController();
	        dbService.update(Constants.QUERY_REMOVE_PART, updatedBy, partNo);

	        response.setStatus(HttpServletResponse.SC_OK);
	        response.getWriter().write("Part marked as deleted successfully.");
	    } catch (Exception e) {
	        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
	        response.getWriter().write("Failed to delete part: " + e.getMessage());
	    }
	}

}
