<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ page import="javax.servlet.http.*,javax.servlet.*"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="ISO-8859-1">
<title>Library</title>
<script>
function makeEditable(td, column, id) {
    if (td.querySelector('input')) return;
 
    const original = td.innerText;
    const input = document.createElement("input");
    input.type = "text";
    input.value = original;
    td.innerHTML = "";
    td.appendChild(input);
    input.focus();
 
    input.addEventListener("keydown", function(e) {
        if (e.key === "Enter") {
            const newValue = input.value;
            const data = new URLSearchParams();
            data.append("id", id);
            data.append("column", column);
            data.append("value", newValue);
 
            fetch("updateBook", {
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                body: data.toString()
            })
            .then(response => {
                if (!response.ok) throw new Error("Network response was not ok");
                return response.text();
            })
            .then(result => {
                td.innerText = newValue;
                location.reload();
            })
            .catch(error => {
                alert("Update failed: " + error.message);
                td.innerText = original;
            });
        } else if (e.key === "Escape") {
            td.innerText = original;
        }
    });
    input.addEventListener("blur", () => {
        td.innerText = original;
    });
}
</script>
</head>
<body>
<form action="login" method="post">
    <input type="hidden" name="action" value="Back">
    <button type="submit">Back</button>
</form>
<br>
 
<table border="1">
    <tr>
        <c:forEach var="column" items="${columns}">
            <th>${column}</th>
        </c:forEach>
    </tr>
    <c:forEach var="row" items="${rows}">
        <tr>
            <c:forEach var="column" items="${columns}">
                <td ondblclick="makeEditable(this, '${column}', '${row.BOOK_ID}')">
                    ${row[column]}
                </td>
            </c:forEach>
        </tr>
    </c:forEach>
</table>
</body>
</html>