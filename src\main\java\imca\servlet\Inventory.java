package imca.servlet;

import java.io.IOException;


import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@WebServlet("/inventory")
public class Inventory extends HttpServlet {
	
	private static final long serialVersionUID = 1L;
	
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		try {
			if(request.getParameter("action").toString().equals("login")) {
				String action = request.getParameter("action");
				request.getRequestDispatcher("/" + action).forward(request, response);
			}
			else if(request.getSession().getAttribute("isAuthenticated") == null) {
				response.sendRedirect(request.getContextPath());
			}
			else if(request.getSession().getAttribute("isAuthenticated").equals("true")) {
				String action = request.getParameter("action");
				request.getRequestDispatcher("/" + action).forward(request, response);
			}
			else {
				response.sendRedirect(request.getContextPath());
			}
		} catch(Exception e) {
			System.err.println(e.getMessage());
		}
	}
	
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		doPost(request, response);
	}

}
