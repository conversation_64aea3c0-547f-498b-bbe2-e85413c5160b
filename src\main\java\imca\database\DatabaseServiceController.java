package imca.database;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import imca.data.Data;
import imca.model.Part;
import imca.util.Constants;
import imca.util.DatabaseConnection;
import imca.util.PreparedStatementt;
import imca.util.Statementt;

public class DatabaseServiceController implements DatabaseService{
	
	@Override
	public PreparedStatement preparedStatement(String Query) throws SQLException {
		
		PreparedStatement statement = DatabaseConnection.getConnect().prepareStatement(Query.trim());
		return statement;

	}
	
	@Override
	public void update(String query, Object... objects) {
		try (PreparedStatement statement = PreparedStatementt.preparedStatement(query)) {
	        for (int i = 0; i < objects.length; i++) {
	        	statement.setObject(i + 1, objects[i]);
	        }
	        statement.executeUpdate();
	    } catch (Exception e) {
	        System.err.println("Update Error: " + e.getMessage());
	    }
	}

	@Override
	public List<HashMap<String, Object>> view(String query) {
	    List<HashMap<String, Object>> results = new ArrayList<>();

	    try (PreparedStatement statement = PreparedStatementt.preparedStatement(query);
	         ResultSet resultSet = statement.executeQuery()) {
	    	
	    	System.out.println(resultSet.toString());
	        ResultSetMetaData metaData = resultSet.getMetaData();
	        System.out.println(metaData.toString());
	        int columnCount = metaData.getColumnCount();

	        while (resultSet.next()) {
	            HashMap<String, Object> row = new HashMap<>();

	            for (int i = 1; i <= columnCount; i++) {
	                String columnName = metaData.getColumnLabel(i);
	                Object value = resultSet.getObject(i);
	                row.put(columnName, value);
	            }

	            results.add(row);
	        }

	    } catch (Exception e) {
	        System.err.println("View Error: " + e.getMessage());
	    }

	    return results;
	}
	
	@Override
	public List<Map<String, Object>> viewTable(String query) {
	    //int count = 0;
		List<Map<String, Object>> tableData = new ArrayList<>();

	    try (PreparedStatement statement = PreparedStatementt.preparedStatement(query);
	         ResultSet resultSet = statement.executeQuery()) {

	        ResultSetMetaData metaData = resultSet.getMetaData();
	        int columnCount = metaData.getColumnCount();

	        while (resultSet.next()) {
	            Map<String, Object> row = new LinkedHashMap<>();
	            for (int i = 1; i <= columnCount; i++) {
	                String columnName = metaData.getColumnLabel(i);
	                Object value = resultSet.getObject(i);
	                row.put(columnName, value);
	                //count++;
	            }
	            tableData.add(row);
	        }

	    } catch (Exception e) {
	        e.printStackTrace();
	    }
	    
	    //System.out.println(count + ">>>>>>>>>>>>>>>>>>>>>>!!!!!!!!!!!!");
	    return tableData;
	}

	@Override
	public String viewHTMLTable(String query) {
	    StringBuilder htmlResult = new StringBuilder();

	    try (PreparedStatement statement = PreparedStatementt.preparedStatement(query);
	         ResultSet resultSet = statement.executeQuery()) {

	        ResultSetMetaData metaData = resultSet.getMetaData();
	        int columnCount = metaData.getColumnCount();

	        // Start the table
	        htmlResult.append("<table border='1' cellpadding='5' cellspacing='0'>");

	        // Table header
	        htmlResult.append("<tr>");
	        for (int i = 1; i <= columnCount; i++) {
	            String columnName = metaData.getColumnLabel(i);
	            htmlResult.append("<th>").append(columnName).append("</th>");
	        }
	        htmlResult.append("</tr>");

	        // Table rows
	        while (resultSet.next()) {
	            htmlResult.append("<tr>");
	            for (int i = 1; i <= columnCount; i++) {
	                Object value = resultSet.getObject(i);
	                htmlResult.append("<td>").append(value != null ? value.toString() : "NULL").append("</td>");
	            }
	            htmlResult.append("</tr>");
	        }

	        // End the table
	        htmlResult.append("</table>");

	    } catch (Exception e) {
	        htmlResult.append("<p style='color:red;'>Error: ").append(e.getMessage()).append("</p>");
	    }

	    return htmlResult.toString();
	}

	
	@Override
	public boolean auth(int USER_ID, String password) {
		
		try (PreparedStatement auth = PreparedStatementt.preparedStatement(Constants.QUERY_AUTH)) {
			auth.setInt(1, USER_ID);

			ResultSet resultSet = auth.executeQuery();
			
			if (resultSet.next()) {
                int userId = resultSet.getInt("USER_ID");
                String name = resultSet.getString("FULL_NAME");
                String dbPassword = resultSet.getString("USER_PASSWORD");

                if (password.equals(dbPassword)) {
                    
                    Constants.properties.setProperty("userId", String.valueOf(userId));
                    Constants.properties.setProperty("userName", name);
                    
                    try (PreparedStatement loginUpdate = PreparedStatementt.preparedStatement(Constants.QUERY_LOGIN_UPDATE)) {
                    	loginUpdate.setInt(1, userId);
                    	loginUpdate.executeUpdate();
                    }

                    return true;
                }
            }
		}catch(Exception e) {
			System.err.println(e.getMessage());
		}

		return false;
	}
	
	@Override
	public boolean insertPart(String partName, String partNo, int initialStock, String desc) {
		
		try{
			try(PreparedStatement addStock = PreparedStatementt.preparedStatement(Constants.QUERY_ADD_STOCK)){
				addStock.setString(1, partNo);
				addStock.setString(2, partName);
				addStock.setInt(3, initialStock);
				addStock.setString(4, desc);
				addStock.setString(5, "API");
				addStock.executeUpdate();
				
				for(int i = 1; i <= initialStock; i++) {
					PreparedStatement tagsStatement = PreparedStatementt.preparedStatement(Constants.QUERY_ADD_TAG);
					Thread.sleep(100);
					tagsStatement.setString(1, (Instant.now().toEpochMilli() + partNo));
					tagsStatement.setString(2, partNo);
					tagsStatement.setString(3, "Servlet");
					tagsStatement.executeUpdate();
				}
				return true;
			}
			
		}catch(Exception e) {
			System.err.println(e.getMessage());
		}
		
		return false;
	}
	
	@Override
	public boolean removePart(String partNo) {
		boolean removePart = false;
		try{			
			ResultSet resultSet = getTags(partNo);
			
			while(resultSet.next()) {
				if(resultSet.getInt("VOID_FLAG") == -1) {
					System.err.println("Part Doesn't Exist!");
				}
				else if(resultSet.getInt("VOID_FLAG") == 1) {
					System.err.println("Part Already Stock Out!");
				}
				else if(resultSet.getInt("VOID_FLAG") == 0) {
			
					PreparedStatement removeTag = PreparedStatementt.preparedStatement(Constants.QUERY_REMOVE_TAG);
					removeTag.setString(1, Constants.properties.getProperty("userName"));
					removeTag.setString(2, resultSet.getString("TAG_NO"));
					removeTag.executeUpdate();
					removePart = true;
				}
			}
			
			if(removePart) {
				PreparedStatement deletePart = PreparedStatementt.preparedStatement(Constants.QUERY_REMOVE_PART);
				deletePart.setString(1, Constants.properties.getProperty("userName"));
				deletePart.setString(2, partNo);
				deletePart.executeUpdate();
				return true;
			}
		}catch(Exception e) {
			System.err.println(e.getMessage());
		}
		
		return false;
	}

	@Override
	public ResultSet getTags(String partNo) {
		
		try {
			PreparedStatement getTag = PreparedStatementt.preparedStatement(Constants.QUERY_GET_TAGS);
			getTag.setString(1, partNo);
			ResultSet resultSet = getTag.executeQuery();
			
			/*
			 * System.out.println("From Get Tags");
			 * 
			 * while(resultSet.next()) { System.out.println(resultSet.getString("TAG_NO"));
			 * System.out.println(resultSet.getInt("VOID_FLAG")); }
			 */
			
			return resultSet;
		} catch (SQLException e) {
			e.printStackTrace();
		}
		
		return null;
	}

	@Override
	public void addAddress(int USER_ID) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Map<String, Part> viewAllParts() {
		
		Map<String, Part> parts = new HashMap<>();
		
		try {
			Statement statement = Statementt.getStatement();
			ResultSet result = statement.executeQuery(Constants.QUERY_DISPLAY_STOCK);
			System.out.println(result);
			while (result.next()) {
				int partId = result.getInt("PART_ID");
				String partNo = result.getString("PART_NO");
				String partName = result.getString("PART_NAME");
				int currentStock = result.getInt("CURRENT_STOCK");
				String partDescription = result.getString("PART_DESCRIPTION");
				
				Part part = new Part(partId, partName, partNo, currentStock, partDescription);
				
				Data.db.put(partNo, part);
				parts.put(partNo, part);
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return parts;
		
	}

	@Override
	public void stockIn(String partNo, int quantity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void stockOut(String partNo, int quantity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Map<String, Part> viewPart(String partNo) {
	    Map<String, Part> partMap = new HashMap<>();

	    try {
	        PreparedStatement statement = preparedStatement(Constants.QUERY_VIEW_PART);
	        statement.setString(1, partNo);
	        ResultSet result = statement.executeQuery();

	        while (result.next()) {
	            int partId = result.getInt("PART_ID");
	            String partName = result.getString("PART_NAME");
	            String partNumber = result.getString("PART_NO");
	            int currentStock = result.getInt("CURRENT_STOCK");
	            String description = result.getString("PART_DESCRIPTION");

	            Part part = new Part(partId, partName, partNumber, currentStock, description);
	            partMap.put(partNumber, part);
	            System.out.println(part);
	        }
	    } catch (Exception e) {
	        System.err.println("Error in viewPart: " + e.getMessage());
	    }

	    return partMap;
	}
	
	@Override
	public String getPassword(int USER_ID) {
		String password = null;
		try {
	        PreparedStatement passwordStatement = preparedStatement(Constants.QUERY_GET_PASSWORD);
	        passwordStatement.setInt(1, USER_ID);
	        ResultSet result = passwordStatement.executeQuery();

	        while (result.next()) {
	            password = result.getString("USER_PASSWORD");
	        }
	    } catch (Exception e) {
	        System.err.println("Error in viewPart: " + e.getMessage());
	    }
		
	    return password;
	}
}
	
