package imca.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class Part implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private int partId;
	private String partName;
	private String partNo;
	private String partDesc;
	private int currentStock;
	private List<Tag> tags = new ArrayList<>();
	
	public Part(int partId, String partName, String partNo, int currentStock, String partDesc) {
		this.partId = partId;
		this.partName = partName;
		this.partNo = partNo;
		this.currentStock = currentStock;
		this.partDesc = partDesc;
	}
	
	public String getPartName() {
		return partName;
	}
	public void setPartName(String partName) {
		this.partName = partName;
	}
	public String getPartNo() {
		return partNo;
	}
	public void setPartNo(String partNo) {
		this.partNo = partNo;
	}
	public String getPartDesc() {
		return partDesc;
	}
	public void setPartDesc(String partDesc) {
		this.partDesc = partDesc;
	}
	public int getCurrentStock() {
		return currentStock;
	}
	public void setCurrentStock(int currentStock) {
		this.currentStock = currentStock;
	}
	public List<Tag> getTags() {
		return tags;
	}
	public void setTags(List<Tag> tags) {
		this.tags = tags;
	}
	public void addTag(Tag tag) {
		tags.add(tag);
	}
	
	public String toString() {
		return "\nPart Name: " + partName +
				"\nPart No: " + partNo +
				"\nCurrent Stock: " + currentStock + 
				"\nPart Description: " + partDesc;
	}

	public int getPartId() {
		return partId;
	}

	public void setPartId(int partId) {
		this.partId = partId;
	}
}
