package imca.database;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import imca.model.Part;

public interface DatabaseService {
	
	public PreparedStatement preparedStatement(String Query) throws SQLException;
	
	public boolean auth(int USER_ID, String password);
	public boolean insertPart(String partName, String partNo, int initialStock, String desc);
	public boolean removePart(String partNo);
	public ResultSet getTags(String partNo);
	public void addAddress(int USER_ID);
	public Map<String, Part> viewAllParts();
	public void stockIn(String partNo, int quantity);
	public void stockOut(String partNo, int quantity);
	public Map<String, Part> viewPart(String partNo);
	public void update(String query, Object...objects);
	public List<HashMap<String, Object>> view(String query);
	public String getPassword(int USER_ID);
	public String viewHTMLTable(String query);
	public List<Map<String, Object>> viewTable(String query);
	
}
