package imca.view;

import java.sql.Connection;
import java.util.InputMismatchException;

import imca.controller.InventoryManagementServiceController;
import imca.util.Constants;
import imca.util.DataSerializer;
import imca.util.DatabaseConnection;
import imca.util.InputHandler;

public class InventoryManagementView {
	
	InventoryManagementServiceController service = new InventoryManagementServiceController();
	
	public void homePage() {	
		
		try {
			do {
				System.out.print("\n1. Add Part\n2. Remove Part\n3. View All Parts\n4. Update Part\n5. Despatch Part\n0. Save & Exit\nEnter your Choice: ");
				switch(InputHandler.readInt()) {
				case 0 -> {
					DataSerializer.saveData();
					System.err.println("Thank you! Visit us again...");
					
					Connection disconnect = DatabaseConnection.getConnect();
					disconnect.close();
					
					return;
				}
				case 1 -> service.addPart();
				case 2 -> service.removePart();
				case 3 -> service.viewAllPart();
				case 4 -> service.updatePart();
				case 5 -> service.despatchPart();
				}
			}while(true);
		}catch (InputMismatchException e) {
			System.err.println(Constants.ERROR_INVALID_CHOICE);
		}catch (Exception e) {
			System.err.println(e.getMessage());
		}
	}
}
