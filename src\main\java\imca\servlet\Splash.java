package imca.servlet;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@WebServlet("/")
public class Splash extends HttpServlet {
	
	private static final long serialVersionUID = 1L;

	protected void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

		try {
			if(request.getSession().getAttribute("isAuthenticated") == null) {
				request.getRequestDispatcher("login.jsp").forward(request, response);
			}
			else if(request.getSession().getAttribute("isAuthenticated").equals("true")) {
				String action = request.getParameter("action");
				if(action != null) {
					request.getRequestDispatcher("/" + action).forward(request, response);
				}else{
					request.getRequestDispatcher("home.jsp").forward(request, response);
				}
			}
			else if(request.getParameter("action").toString().equals("login")) {
				String action = request.getParameter("action");
				request.getRequestDispatcher("/" + action).forward(request, response);
			}
			else {
				response.sendRedirect(request.getContextPath());
			}
		} catch(Exception e) {
			System.err.println(e.getMessage());
		}
	}
}