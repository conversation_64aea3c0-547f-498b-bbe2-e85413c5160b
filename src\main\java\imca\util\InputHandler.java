package imca.util;

import java.util.Scanner;

public class InputHandler {
	
	public static Scanner read;
	
	public static int readInt() {
		read = new Scanner(System.in);
		return read.nextInt();
	}
	
	public static String readLine() {
		read = new Scanner(System.in);
		return read.nextLine();
	}
	
	public static String readWord() {
		read = new Scanner(System.in);
		return read.next();
	}
	
	public static long readLong() {
		read = new Scanner(System.in);
		return read.nextLong();
	}
	
	public static double readDouble() {
		read = new Scanner(System.in);
		return read.nextDouble();
	}

}
