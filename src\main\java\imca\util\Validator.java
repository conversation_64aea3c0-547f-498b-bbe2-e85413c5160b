package imca.util;

import java.util.regex.Pattern;

public class Validator {
	
	public static boolean isValidPartNo(String partNo){
		return Pattern.matches("^[\\w.]{9}$", partNo);
	}

	public static boolean isValidPartName(String partName){
		return Pattern.matches("^[\\w\\s]{3,}$", partName);
	}
	
	public static boolean isStock(int quantity){
		return quantity > 0;
	}
	
	public static boolean isValidPincode(int pincode){
		String pinCode = String.valueOf(pincode);
		return Pattern.matches("^[0-9]{6}$", pinCode);
	}
}
