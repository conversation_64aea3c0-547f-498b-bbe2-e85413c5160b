package imca.servlet;

import java.io.IOException;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import imca.database.DatabaseServiceController;
import imca.util.Constants;
import imca.util.ConverterHTML;

@WebServlet("/viewpart")
public class ViewPart extends HttpServlet {
	
	private static final long serialVersionUID = 1L;

	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		
		if(request.getSession().getAttribute("isAuthenticated").equals("true")) {
			DatabaseServiceController dbService = new DatabaseServiceController();
			//String htmlContent = ConverterHTML.convertPartsMapToHtml(dbService.viewPart(request.getParameter("partno")));
			
			response.setContentType("text/html");
			response.getWriter().append(dbService.viewHTMLTable(Constants.QUERY_DISPLAY_STOCK));
		}
	}

}