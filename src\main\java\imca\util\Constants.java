package imca.util;

import java.util.Properties;

public class Constants {
	
	// Path, Credentials & Properties
	public static final String PATH_ROOT = "res";
	public static final String PATH_PROPERTIES = "/config.properties";
	public static final String PATH_SERIALIZATION = "/inventory.dat";
	public static Properties properties = new Properties();
	public static final String PROD_ACCESS_PASSWORD = "root";
	
	// Error Messages
	public static final String ERROR_INVALID_CHOICE = "Invlaid Choice!";
	public static final String ERROR_INVALID_FORMAT = "Invalid Data Format";
	
	// Query
	public static final String QUERY_AUTH = "SELECT USER_ID, FULL_NAME, MAIL_ID, USER_PASSWORD FROM USERS WHERE USER_ID = ?";
	public static final String QUERY_VIEW_PART = "SELECT * FROM PART WHERE PART_NO = ?";
	public static final String QUERY_LOGIN_UPDATE = "UPDATE USERS SET LOGIN_TIMESTAMP = GETDATE() WHERE USER_ID = ?";
	public static final String QUERY_ADD_STOCK =  "INSERT INTO PART (PART_NO, PART_NAME, CURRENT_STOCK, PART_DESCRIPTION, CREATED_BY) VALUES (?, ?, ?, ?, ?)";
	public static final String QUERY_ADD_TAG = "INSERT INTO TAG (TAG_NO, PART_NO, CREATED_BY) VALUES (?, ?, ?)";
	public static final String QUERY_DISPLAY_STOCK =  "SELECT PART_ID [ID], PART_NO [PART NO], PART_NAME [PART NAME], CURRENT_STOCK [CURRENT STOCK], PART_DESCRIPTION [PART DESCRIPTION] FROM PART WHERE VOID_FLAG IS NULL";
	public static final String QUERY_REMOVE_PART = "UPDATE PART SET VOID_FLAG = 0 , UPDATED_BY = ? , UPDATED_TIMESTAMP = GETDATE() WHERE PART_NO = ?";
	public static final String QUERY_GET_TAGS = "SELECT TAG_NO, VOID_FLAG FROM TAG WHERE PART_NO = ?";
	public static final String QUERY_REMOVE_TAG = "UPDATE TAG SET VOID_FLAG = 0 , UPDATED_BY = ? , UPDATED_TIMESTAMP = GETDATE() WHERE TAG_NO = ?";
	public static final String QUERY_GET_PASSWORD = "SELECT USER_PASSWORD FROM USERS WHERE USER_ID = ?";
	public static final String QUERY_CREATE_USER = "INSERT INTO USERS (FULL_NAME, MAIL_ID, USER_PASSWORD, CREATED_BY) VALUES (?, ?, ?, ?)";
}
