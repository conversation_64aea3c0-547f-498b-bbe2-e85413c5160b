package imca.servlet;

import java.io.IOException;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import imca.database.DatabaseServiceController;

@WebServlet("/login")
public class Login extends HttpServlet {
	
	private static final long serialVersionUID = 1L;

	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		
//		// request.getRequestDispatcher("test.jsp").forward(request, response);
		int userId = Integer.parseInt(request.getParameter("userId"));
		String password = request.getParameter("password");
		
		// String encPassword = null;

		DatabaseServiceController dbService = new DatabaseServiceController();
		boolean authState = dbService.auth(userId, password);
		
		try {
			if(authState) {
				request.getSession().setAttribute("userId", userId);
				request.getSession().setAttribute("isAuthenticated", "true");
				request.getSession().setMaxInactiveInterval(30);
				
				Cookie userCookie = new Cookie("userId", String.valueOf(userId));
				userCookie.setHttpOnly(true);  
				userCookie.setPath(request.getContextPath());
				userCookie.setMaxAge(-1);           
				response.addCookie(userCookie);
				
				response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
				request.getRequestDispatcher("home.jsp").forward(request, response);
			}else {
				response.sendRedirect(request.getContextPath());
			}
		} catch (Exception e) {
			System.err.println(e.getMessage());
		}
		
	}

}
