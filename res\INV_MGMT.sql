USE INVENTORY_MANAGEMENT;

CREATE TABLE USERS(
	USER_ID INT IDENTITY(1001,1) PRIMARY KEY,
	FULL_NAME VARCHAR(30) NOT NULL,
	MAIL_ID VARCHAR(50) UNIQUE NOT NULL,
	USER_PASSWORD VARCHAR(50) NOT NULL,
	CREATED_BY VARCHAR(50) NOT NULL,
	CREATED_TIMESTAMP DATETIME DEFAULT GETDATE() NOT NULL,
	LOGIN_TIMESTAMP DATETIME,
	UPDATED_BY VARCHAR(50),
	UPDATED_TIMESTAMP DATETIME,
	VOID_FLAG INT,
);

CREATE TABLE USER_ADDRESS (
	ADDRESS_ID INT IDENTITY(1,1) PRIMARY KEY,
    USER_ID INT,
    BUILDING_NAME VARCHAR(20) NOT NULL,
	LANE VARCHAR(30) NOT NULL,
	LAYOUT VARCHAR(30) NOT NULL,
    CITY VARCHAR(30) NOT NULL,
    STATE VARCHAR(30) NOT NULL,
    COUNTRY VARCHAR(30) NOT NULL,
	PIN_CODE VARCHAR(6) NOT NULL,
	CREATED_BY VARCHAR(50) NOT NULL,
	CREATED_TIMESTAMP DATETIME DEFAULT GETDATE() NOT NULL,
	UPDATED_BY VARCHAR(50),
	UPDATED_TIMESTAMP DATETIME,
	VOID_FLAG INT,
		FOREIGN KEY (USER_ID) REFERENCES USERS(USER_ID),
);

CREATE TABLE PART(
	PART_ID INT IDENTITY(1,1) UNIQUE,
	PART_NO VARCHAR(9) CHECK(PART_NO LIKE '____.____') NOT NULL PRIMARY KEY,
	PART_NAME VARCHAR(50) NOT NULL,
	CURRENT_STOCK INT NOT NULL CHECK(CURRENT_STOCK >= 0),
	PART_DESCRIPTION VARCHAR(50),
	CREATED_BY VARCHAR(50) NOT NULL,
	CREATED_TIMESTAMP DATETIME DEFAULT GETDATE() NOT NULL,
	UPDATED_BY VARCHAR(50),
	UPDATED_TIMESTAMP DATETIME,
	VOID_FLAG INT,
);

CREATE TABLE TAG(
	TAG_ID INT IDENTITY(1,1) UNIQUE NOT NULL,
	TAG_NO VARCHAR(22) PRIMARY KEY,
	PART_NO VARCHAR(9),
	CREATED_BY VARCHAR(50) NOT NULL,
	CREATED_TIMESTAMP DATETIME DEFAULT GETDATE() NOT NULL,
	UPDATED_BY VARCHAR(50),
	UPDATED_TIMESTAMP DATETIME,
	VOID_FLAG INT,
		FOREIGN KEY (PART_NO) REFERENCES PART(PART_NO),
);

CREATE TABLE SHIPMENT_DETAILS (
	SD_ID INT IDENTITY(1,1) UNIQUE NOT NULL,
	SHIPMENT_DETAILS_ID VARCHAR(9) PRIMARY KEY,
	USER_ID INT NOT NULL,
	ADDRESS_ID INT NOT NULL,
	PART_NO VARCHAR(9)  NOT NULL,
    TAG_NO VARCHAR(22) NOT NULL,
	CREATED_BY VARCHAR(50) NOT NULL,
	CREATED_TIMESTAMP DATETIME DEFAULT GETDATE() NOT NULL,
	UPDATED_BY VARCHAR(50),
	UPDATED_TIMESTAMP DATETIME,
	VOID_FLAG INT,
		FOREIGN KEY (USER_ID) REFERENCES USERS(USER_ID),
		FOREIGN KEY (ADDRESS_ID) REFERENCES USER_ADDRESS(ADDRESS_ID),
		FOREIGN KEY (PART_NO) REFERENCES PART(PART_NO),
		FOREIGN KEY (TAG_NO) REFERENCES TAG(TAG_NO),
);

CREATE TABLE SHIPMENT (
    SHIPMENT_ID VARCHAR(8) PRIMARY KEY,
	SHIPMENT_DETAILS_ID VARCHAR(9),
    SHIPMENT_CREATED DATETIME DEFAULT GETDATE() NOT NULL,
	SHIPMENT_DELIVERED DATETIME,
	SHIPMENT_STATE VARCHAR(30),
	CREATED_BY VARCHAR(50) NOT NULL,
	CREATED_TIMESTAMP DATETIME DEFAULT GETDATE() NOT NULL,
	UPDATED_BY VARCHAR(50),
	UPDATED_TIMESTAMP DATETIME,
	VOID_FLAG INT,
		FOREIGN KEY (SHIPMENT_DETAILS_ID) REFERENCES SHIPMENT_DETAILS(SHIPMENT_DETAILS_ID),
);

INSERT INTO USERS (FULL_NAME, MAIL_ID, USER_PASSWORD, CREATED_BY)
VALUES 
('John Doe', '<EMAIL>', 'password123', 'admin'),
('Jane Smith', '<EMAIL>', 'password123', 'admin'),
('Bob Johnson', '<EMAIL>', 'password123', 'admin'),
('Alice Brown', '<EMAIL>', 'password123', 'admin'),
('Charlie Davis', '<EMAIL>', 'password123', 'admin');
INSERT INTO USER_ADDRESS (USER_ID, BUILDING_NAME, LANE, LAYOUT, CITY, STATE, COUNTRY, PIN_CODE, CREATED_BY)
VALUES 
(1001, 'Building A', 'Lane 101', 'Layout 1', 'New York', 'NY', 'USA', '10001', 'admin'),
(1002, 'Building B', 'Lane 102', 'Layout 2', 'Los Angeles', 'CA', 'USA', '90001', 'admin'),
(1003, 'Building C', 'Lane 103', 'Layout 3', 'Chicago', 'IL', 'USA', '60007', 'admin'),
(1004, 'Building D', 'Lane 104', 'Layout 4', 'Houston', 'TX', 'USA', '77001', 'admin'),
(1005, 'Building E', 'Lane 105', 'Layout 5', 'Phoenix', 'AZ', 'USA', '85001', 'admin');
INSERT INTO PART (PART_NO, PART_NAME, CURRENT_STOCK, PART_DESCRIPTION, CREATED_BY)
VALUES 
('PART.0010', 'Part A',		, 'Description for Part A', 'admin'),
('PART.0020', 'Part B', 30, 'Description for Part B', 'admin'),
('PART.0030', 'Part C', 100, 'Description for Part C', 'admin'),
('PART.0040', 'Part D', 20, 'Description for Part D', 'admin'),
('PART.0050', 'Part E', 75, 'Description for Part E', 'admin');
INSERT INTO TAG (TAG_NO, PART_NO, CREATED_BY)
VALUES 
('TAG001', 'PART.0010', 'admin'),
('TAG002', 'PART.0020', 'admin'),
('TAG003', 'PART.0030', 'admin'),
('TAG004', 'PART.0040', 'admin'),
('TAG005', 'PART.0050', 'admin');
INSERT INTO SHIPMENT_DETAILS (SHIPMENT_DETAILS_ID, USER_ID, ADDRESS_ID, PART_NO, TAG_NO, CREATED_BY)
VALUES 
('SD001', 1001, 1, 'PART.0010', 'TAG001', 'admin'),
('SD002', 1002, 2, 'PART.0020', 'TAG002', 'admin'),
('SD003', 1003, 3, 'PART.0030', 'TAG003', 'admin'),
('SD004', 1004, 4, 'PART.0040', 'TAG004', 'admin'),
('SD005', 1005, 5, 'PART.0050', 'TAG005', 'admin');
INSERT INTO SHIPMENT (SHIPMENT_ID, SHIPMENT_DETAILS_ID, SHIPMENT_CREATED, SHIPMENT_DELIVERED, SHIPMENT_STATE, CREATED_BY)
VALUES 
('SHIP001', 'SD001', GETDATE(), DATEADD(DAY, 5, GETDATE()), 'Delivered', 'admin'),
('SHIP002', 'SD002', GETDATE(), DATEADD(DAY, 7, GETDATE()), 'Shipped', 'admin'),
('SHIP003', 'SD003', GETDATE(), DATEADD(DAY, 10, GETDATE()), 'Shipped', 'admin'),
('SHIP004', 'SD004', GETDATE(), DATEADD(DAY, 4, GETDATE()), 'Delivered', 'admin'),
('SHIP005', 'SD005', GETDATE(), DATEADD(DAY, 6, GETDATE()), 'Shipped', 'admin');

DELETE FROM SHIPMENT
WHERE SHIPMENT_ID = 'SHIP005';

UPDATE SHIPMENT SET SHIPMENT_ID = 'SHIP100' WHERE SHIPMENT_DETAILS_ID = 'SD004';

UPDATE PART SET VOID_FLAG = 0 , UPDATED_BY = 'John Doe' , UPDATED_TIMESTAMP = GETDATE() WHERE PART_NO = 'MUST.1234';
SELECT TAG_NO, VOID_FLAG FROM TAG WHERE PART_NO = 'MUST.1234';

INSERT INTO 
EVENTS (EVENT_ID, EVENT_NA ME, EVENT_DESCRIPTION) 
VALUES ('EVENT1001', 'UserCreation', 'bySYSDBA');

INSERT INTO 
USERS (ID, FULL_NAME, MAIL_ID, USER_PASSWORD, EVENT_ID) 
VALUES ('USER1001', 'Suriya', 'hello@krit', '1928@AbCd', 'EVENT1001');

INSERT INTO 
EVENTS (EVENT_ID, EVENT_NAME, EVENT_DESCRIPTION) 
VALUES ('EVENT1002', 'addressCreation', 'bySYSDBA-USER1000');

INSERT INTO
USER_ADDRESS(ADDRESS_ID, BUILDING_NAME, LANE, LAYOUT, CITY, STATE, COUNTRY, PIN_CODE, USER_ID_NO, EVENT_ID)
VALUES('ADDRESS1001', 'krithvikHouse', 'lane', 'layout', 'cincinnati', 'ohio', 'usa', '654321', 'USER1001', 'EVENT1002');

INSERT INTO 
EVENTS (EVENT_ID, EVENT_NAME, EVENT_DESCRIPTION) 
VALUES ('EVENT1003', 'partCreation', 'bySYSDBA');

INSERT INTO 
PART(PART_ID, PART_NAME, CURRENT_STOCK, PART_DESCRIPTION, EVENT_ID) 
VALUES ('PART1001', 'GEAR', 2, 'Gear Description', 'EVENT1003');

INSERT INTO 
EVENTS (EVENT_ID, EVENT_NAME, EVENT_DESCRIPTION) 
VALUES ('EVENT1004', 'tagCreation', 'bySYSDBA-PART1001');
INSERT INTO 
TAG(TAG_ID, PART_ID, EVENT_ID) 
VALUES ('TAG1001', 'PART1001', 'EVENT1004');

INSERT INTO 
EVENTS (EVENT_ID, EVENT_NAME, EVENT_DESCRIPTION) 
VALUES ('EVENT1005', 'tagCreation', 'bySYSDBA-PART1001');
INSERT INTO 
TAG(TAG_ID, PART_ID, EVENT_ID) 
VALUES ('TAG1002', 'PART1001', 'EVENT1005');

INSERT INTO 
EVENTS (EVENT_ID, EVENT_NAME, EVENT_DESCRIPTION) 
VALUES ('EVENT1006', 'shipping', 'byUSER1000-TAG1001');
INSERT INTO 
SHIPMENT_DETAILS(SHIPMENT_DETAILS_ID, USER_ID_NO, TAG_ID, PART_ID, ADDRESS_ID, EVENT_ID) 
VALUES ('SHIPD1001', 'USER1001', 'TAG1001', 'PART1001', 'ADDRESS1001', 'EVENT1006');
INSERT INTO 
SHIPMENT(SHIPMENT_ID, SHIPMENT_DETAILS_ID, SHIPMENT_STATE, EVENT_ID) 
VALUES ('SHIP1001', 'SHIPD1001', 'preparing', 'EVENT1006');

SELECT * FROM USERS;
SELECT * FROM USER_ADDRESS;
SELECT * FROM PART;
SELECT * FROM TAG;
SELECT * FROM SHIPMENT_DETAILS;
SELECT * FROM SHIPMENT;

SELECT 
	USERS.USER_ID,
	USERS.FULL_NAME,
	USERS.MAIL_ID,
	USERS.CREATED_TIMESTAMP AS USER_CREATED_AT,

	USER_ADDRESS.BUILDING_NAME,
	USER_ADDRESS.LANE,
	USER_ADDRESS.LAYOUT,
	USER_ADDRESS.CITY,
	USER_ADDRESS.STATE,
	USER_ADDRESS.COUNTRY,
	USER_ADDRESS.PIN_CODE,

	PART.PART_NO,
	PART.PART_NAME,
	PART.CURRENT_STOCK,
	PART.PART_DESCRIPTION,

	TAG.TAG_NO,

	SHIPMENT_DETAILS.SD_ID,
	SHIPMENT_DETAILS.CREATED_TIMESTAMP AS ORDER_CREATED,
	
	SHIPMENT.SHIPMENT_DETAILS_ID AS TRACKING_ID,
	SHIPMENT.SHIPMENT_STATE AS DELIVERY_STATUS
FROM USERS
JOIN USER_ADDRESS ON USERS.USER_ID = USER_ADDRESS.USER_ID
JOIN SHIPMENT_DETAILS ON SHIPMENT_DETAILS.USER_ID = USERS.USER_ID
					 AND SHIPMENT_DETAILS.ADDRESS_ID = USER_ADDRESS.ADDRESS_ID
JOIN PART ON PART.PART_NO = SHIPMENT_DETAILS.PART_NO
JOIN TAG ON TAG.PART_NO = PART.PART_NO
JOIN SHIPMENT ON SHIPMENT.SHIPMENT_DETAILS_ID = SHIPMENT_DETAILS.SHIPMENT_DETAILS_ID;


DROP TABLE SHIPMENT;
DROP TABLE SHIPMENT_DETAILS;
DROP TABLE TAG;
DROP TABLE PART;
DROP TABLE USER_ADDRESS;
DROP TABLE USERS;

-- TRUNCATE TABLE USERS;
-- SAVEPOINT;
-- ROLLBACK;
BEGIN TRANSACTION;
COMMIT TRANSACTION;
