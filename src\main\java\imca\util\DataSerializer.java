package imca.util;

import java.io.*;
import java.util.Map;

import imca.data.Data;
import imca.model.Part;

public class DataSerializer {
	
	public static void saveData() throws FileNotFoundException, IOException {
		
		try(ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(Constants.PATH_ROOT + Constants.PATH_SERIALIZATION))){
			oos.writeObject(Data.db);
		}catch (Exception e) {
			System.err.println(e.getMessage());
		}

	}
	
	@SuppressWarnings("unchecked")
	public static void loadData() {
		
		File file = new File(Constants.PATH_ROOT + Constants.PATH_SERIALIZATION);
		
		if(!file.exists()) {
			return;
		}
		
		try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(Constants.PATH_ROOT))){
			
			Data.db = (Map<String, Part>) ois.readObject();

		}catch (Exception e) {
			System.err.println(e.getMessage());
		}
		
	}

}
