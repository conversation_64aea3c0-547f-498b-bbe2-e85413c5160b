package imca.controller;

import java.io.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.InputMismatchException;
import java.util.Map;

import imca.data.Data;
import imca.database.DatabaseServiceController;
import imca.model.Address;
import imca.model.Part;
import imca.model.Tag;
import imca.util.Constants;
import imca.util.DatabaseConnection;
import imca.util.InputHandler;
import imca.util.Validator;

public class InventoryManagementServiceController implements InventoryManagementService {
	
	DatabaseServiceController dbService = new DatabaseServiceController();
	
	@Override
	public void init() throws FileNotFoundException, IOException {

		try {
			String fileLocation = Constants.PATH_ROOT + Constants.PATH_PROPERTIES;

			Constants.properties.load(new FileInputStream(fileLocation));
			// DataSerializer.loadData();

		} catch (Exception e) {
			System.err.println(e.getMessage());
		}

	}

	@Override
	public void addPart() {
		try {
			System.out.print("Enter Part Name: ");
			String partName = InputHandler.readLine();

			System.out.print("Enter Part Number: ");
			String partNo = InputHandler.readWord();

			System.out.print("Enter Initial Stock: ");
			int initialStock = InputHandler.readInt();

			System.out.print("Enter Part Description: ");
			String desc = InputHandler.readLine();

			// String tagId = (Instant.now().toEpochMilli() + partNo);

			if (Validator.isValidPartName(partName) && Validator.isValidPartNo(partNo) && Validator.isStock(initialStock)) {
				
				if(dbService.insertPart(partName, partNo, initialStock, desc)) {
					System.out.println(partName + " Created Successfully!");
				}else {
					System.err.println("Server is down check back later...");
				}
				
				/*
				 * Tag tag = new Tag(tagId, initialStock); Part part = new Part(partName,
				 * partNo, initialStock, tag, desc.trim());
				 * 
				 * Data.db.put(partNo, part); Data.tags.put(tagId, tag);
				 * 
				 * //DataSerializer.saveData(); System.out.println(tag);
				 */

			} else {
				System.err.println(Constants.ERROR_INVALID_FORMAT);
			}
		} catch (InputMismatchException e) {
			System.err.println(Constants.ERROR_INVALID_FORMAT);
		} catch (Exception e) {
			System.err.println(e.getMessage());
		}

	}

	@Override
	public void removePart() {
		
		try {
			System.out.print("Enter Part No:");
			String partNo = InputHandler.readWord();
			
			if(Validator.isValidPartNo(partNo)) {
				if (dbService.removePart(partNo)) {
					System.out.println(partNo + " Deleted Successfully");
				} else {
					System.err.println("Server is Down Kindly try again later contact Support team.");
				}
			}
		}catch(Exception e) {
			System.err.println(e.getMessage());
		}
		
			
			/*if(Data.db.containsKey(partNo)) {
				System.out.print("Confirm Delete type \"Delete\" to delete: ");
				String message = InputHandler.readWord();
				
				if(message.equalsIgnoreCase("Delete")) {
					Data.db.get(partNo).getTags().forEach(e -> Data.tags.remove(e));
					System.out.println(Data.db.get(partNo).getPartName() + " Part Removed");
					Data.db.remove(partNo);	
					System.out.println("Item Deleted Successfully");
				}else {
					System.err.println("Item Not Deleted");
				}
			}
			else {
				System.err.println("Part Not Found!");
			}*/
				

		
	}

	@Override
	public void viewAllPart() {
		
		dbService.viewAllParts();

		if(Data.db.size() != 0) {
			for(Map.Entry<String, Part> entry : Data.db.entrySet()) {
				System.out.println("-------- Part Id: " + entry.getKey() + " --------"
						+ entry.getValue());
			}
		}
		else {
			System.err.println("The Inventory is empty");
		}

	}

	@Override
	public void updatePart() {

		try {
			System.out.print("Enter Part No:");
			String partNo = InputHandler.readWord();

			if (Data.db.containsKey(partNo)) {
				System.out.println(Data.db.get(partNo));

				System.out.print("1. Stock In\n2. Stock Out\nEnter your Choice; ");

				switch (InputHandler.readInt()) {

				case 1 -> {
					System.out.print("Enter Stock In Quantity: ");
					int quantity = InputHandler.readInt();

					if (Validator.isStock(quantity)) {
						String tagId = (Instant.now().toEpochMilli() + partNo);
						Tag tag = new Tag(tagId, quantity);

						Data.db.get(partNo).addTag(tag);
						Data.tags.put(tagId, tag);

						int curentStock = Data.db.get(partNo).getCurrentStock();
						Data.db.get(partNo).setCurrentStock(curentStock + quantity);
						System.out.println("Item Updated Successfully");
					} else {
						System.err.println("Invalid Quantity");
					}
				}
				case 2 -> {
					System.out.print("Enter Stock Out Quantity: ");
					int quantity = InputHandler.readInt();

					if (quantity <= Data.db.get(partNo).getCurrentStock()) {
						Data.db.get(partNo).getTags().forEach(System.out::println);
						System.out.print("Enter the Tag Id: ");
						String localTagId = InputHandler.readWord();

						int tagQuantity = Data.tags.get(localTagId).getTagQuantity();
						if (quantity <= tagQuantity) {
							int curentStock = Data.db.get(partNo).getCurrentStock();
							Data.db.get(partNo).setCurrentStock(curentStock - quantity);
							Data.tags.get(localTagId).setTagQuantity(tagQuantity - quantity);
							System.out.println("Item Updated Successfully");
						} else {
							System.err.println("Insufficent Stock..");
						}
					} else {
						System.err.println("Insufficent Stock..");
					}
				}
				default -> System.err.println(Constants.ERROR_INVALID_CHOICE);
				}
			} else {
				System.err.println("Part Not Found!");
			}

		} catch (InputMismatchException e) {
			System.err.println(Constants.ERROR_INVALID_CHOICE);
		} catch (Exception e) {
			System.err.println(e.getMessage());
		}
	}

	@Override
	public void despatchPart() {

		try {
			System.out.print("Enter Part No:");
			String partNo = InputHandler.readWord();

			if (Data.db.containsKey(partNo)) {
				System.out.print("Enter Quantity to Shipping: ");
				int quantity = InputHandler.readInt();

				if (quantity <= Data.db.get(partNo).getCurrentStock()) {
					Data.db.get(partNo).getTags().forEach(System.out::println);
					System.out.print("Enter the Tag Id: ");
					String localTagId = InputHandler.readWord();

					int tagQuantity = Data.tags.get(localTagId).getTagQuantity();
					if (quantity <= tagQuantity) {

						System.out.print("Enter building Name: ");
						String buildingName = InputHandler.readLine();

						System.out.print("Enter Lane: ");
						String lane = InputHandler.readLine();

						System.out.print("Enter Layout: ");
						String layout = InputHandler.readLine();

						System.out.print("Enter City: ");
						String city = InputHandler.readLine();

						System.out.print("Enter State: ");
						String state = InputHandler.readLine();

						System.out.print("Enter Country: ");
						String country = InputHandler.readLine();

						System.out.print("Enter Pincode: ");
						int pincode = InputHandler.readInt();

						if (Validator.isValidPincode(pincode)) {
							Address address = new Address(buildingName, lane, layout, city, state, country, pincode);

							int curentStock = Data.db.get(partNo).getCurrentStock();
							Data.db.get(partNo).setCurrentStock(curentStock - quantity);
							Data.tags.get(localTagId).setTagQuantity(tagQuantity - quantity);
							Data.despatch.put(localTagId, address);
							System.out.println("Item Despatched Successfully");

						} else {
							System.err.println("Invalid Address");
						}
					} else {
						System.err.println("Insufficent Stock..");
					}
				} else {
					System.err.println("Insufficent Stock..");
				}
			} else {
				System.err.println("Part Not Found!");
			}

		} catch (Exception e) {
			System.err.println(e.getMessage());
		}

	}
}
