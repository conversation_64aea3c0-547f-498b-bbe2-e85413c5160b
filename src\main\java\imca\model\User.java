package imca.model;

public class User {
	
	private int userId;
	private String userName;
	private String mailId;
	private String password;
	
	public User(int userId, String userName, String mailId, String password) {
		this.userId = userId;
		this.userName = userName;
		this.mailId = mailId;
		this.password = password;
	}
	
	public int getUserId() {
		return userId;
	}
	public void setUserId(int userId) {
		this.userId = userId;
	}
	public String getuserName() {
		return userName;
	}
	public void setuserName(String userName) {
		this.userName = userName;
	}
	public String getMailId() {
		return mailId;
	}
	public void setMailId(String mailId) {
		this.mailId = mailId;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}

}
