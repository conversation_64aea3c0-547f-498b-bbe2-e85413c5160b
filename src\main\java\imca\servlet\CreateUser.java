package imca.servlet;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import imca.database.DatabaseServiceController;
import imca.util.Constants;


@WebServlet("/createuser")
public class CreateUser extends HttpServlet {
	private static final long serialVersionUID = 1L;

	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		
		String name = request.getParameter("name");
		String mailId = request.getParameter("mail");
		String password = request.getParameter("password");
		
		DatabaseServiceController dbService = new DatabaseServiceController();
		
		dbService.update(Constants.QUERY_CREATE_USER, name, mailId, password);
	}

}
