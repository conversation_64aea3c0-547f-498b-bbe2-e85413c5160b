package imca.util;

import java.util.List;
import java.util.Map;

import imca.model.Part;

public class ConverterHTML {
	
	public static String convertPartsMapToHtml(Map<String, Part> partMap) {
        StringBuilder html = new StringBuilder();
        
        
        html.append("<html><body>").append("<h2>Part Details</h2>");
        html.append("<table border='1' cellpadding='5' cellspacing='0'>");
        html.append("<tr>")
            .append("<th>ID</th>")
            .append("<th>Part No</th>")
            .append("<th>Name</th>")
            .append("<th>Stock</th>")
            .append("<th>Description</th>")
            .append("</tr>");

        for (Map.Entry<String, Part> entry : partMap.entrySet()) {
            Part part = entry.getValue();

            html.append("<tr>")
                .append("<td>").append(part.getPartId()).append("</td>")
                .append("<td>").append(part.getPartNo()).append("</td>")
                .append("<td>").append(part.getPartName()).append("</td>")
                .append("<td>").append(part.getCurrentStock()).append("</td>")
                .append("<td>").append(part.getPartDesc()).append("</td>")
                .append("</tr>");
        }

        html.append("</table>");
        html.append("</body></html>");
        return html.toString();
    }
	
	public static String convertToHtmlTable(List<Map<String, Object>> data) {
	    if (data == null || data.isEmpty()) {
	        return "<p>No data available</p>";
	    }

	    StringBuilder html = new StringBuilder();
	    html.append("<table border='1' cellpadding='5' cellspacing='0'>");

	    // Header row
	    html.append("<tr>");
	    for (String column : data.get(0).keySet()) {
	        html.append("<th>").append(column).append("</th>");
	    }
	    html.append("</tr>");

	    for (Map<String, Object> row : data) {
	        html.append("<tr>");
	        for (Object value : row.values()) {
	            html.append("<td>").append(value != null ? value.toString() : "NULL").append("</td>");
	        }
	        html.append("</tr>");
	    }

	    html.append("</table>");
	    return html.toString();
	}
	
	public static String formAction(String action) {
	    StringBuilder html = new StringBuilder();

	    html.append("<form action=\"inventory\" method=\"POST\">");
	    html.append("<input type=\"hidden\" name=\"action\" value=\"");
	    html.append(action);
	    html.append("\">");
	    html.append("<input type=\"submit\" value=\"");
	    html.append(action.toUpperCase());
	    html.append("\">");
	    html.append("</form>");

	    return html.toString();
	}

}