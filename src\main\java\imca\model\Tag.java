package imca.model;

import java.io.Serializable;

public class Tag implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	private String tagId;
	private int tagQuantity;
	
	public Tag(String tagId, int tagQuantity) {
		this.tagId = tagId;
		this.tagQuantity = tagQuantity;
	}
	
	public String getTagId() {
		return tagId;
	}
	public void setTagId(String tagId) {
		this.tagId = tagId;
	}
	public int getTagQuantity() {
		return tagQuantity;
	}
	public void setTagQuantity(int tagQuantity) {
		this.tagQuantity = tagQuantity;
	}	
	
	public String toString() {
		return "\nTag Id: " + tagId + " Tag Quantity: " + tagQuantity;
	}
}
