package imca.servlet;

import java.io.IOException;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import imca.database.DatabaseServiceController;

@WebServlet("/addpart")
public class AddPart extends HttpServlet {
	
	private static final long serialVersionUID = 1L;

	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		String partName = request.getParameter("partName");
	    String partNo = request.getParameter("partNo");
	    String desc = request.getParameter("desc");
	    int initialStock = Integer.parseInt(request.getParameter("initialStock"));

	    DatabaseServiceController dbService = new DatabaseServiceController();
	    boolean status = dbService.insertPart(partName, partNo, initialStock, desc);

	    response.setContentType("text/plain");
	    response.getWriter().write(status ? "Part added successfully" : "Failed to add part");
	}

}
